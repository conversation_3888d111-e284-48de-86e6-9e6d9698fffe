import 'package:built_collection/built_collection.dart';
import 'package:fusion/api/api.dart';
import 'package:fusion/api/api_response.dart';
import 'package:fusion/events/order_cancelled_event.dart';
import 'package:fusion/events/trade_offer_accept_event.dart';
import 'package:fusion/events/trade_offer_created_event.dart';
import 'package:fusion/keys.dart';
import 'package:fusion/pages/mixin/page_state_mixin.dart';
import 'package:fusion/pages/mixin/trade_offer_accept_event_listener.dart';
import 'package:fusion/pages/mixin/trade_offer_created_event_listener.dart';
import 'package:fusion/pages/order/mixin/guard_mixin.dart';
import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/router/app_router.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/utils/create_order_stickers.dart';
import 'package:fusion/utils/event_bus.dart';
import 'package:fusion/utils/styled_accessory_finder.dart';
import 'package:fusion/widgets/toast.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:result_dart/result_dart.dart';

import '../model/order_detail_data.dart';

class OrderDetailController extends FullLifeCycleController
    with
        PageStateMixin,
        GuardMixin,
        TradeOfferCreatedEventListener,
        TradeOfferAcceptEventListener,
        FullLifeCycleMixin {
  /// 查询的订单id
  final String orderId;

  /// 所有数据
  final data = Rx<OrderDetailData?>(null);

  /// 交易保护刷新状态
  final tradeProtectionRefreshing = false.obs;

  /// 状态信息
  String get stateMessage => data.value?.stateMessage ?? "";

  /// 倒计时
  // int get countdown => data.value?.orderStatusCountdown ?? 0;
  int get countdown => 86419;

  /// 是否需要显示令牌信息。交易中且报价已创建的的订单才会显示
  bool get needsBuyerGuard {
    final d = data.value;
    if (d == null) {
      return false;
    }
    return d.buyerGuard != null &&
        d.isBuyer == false &&
        [
          OrderState.waitingResponseTradeOffer,
          OrderState.waitingConfirmTradeOffer,
          OrderState.waitingReceiveTradeOffer,
          OrderState.tradeOfferPending,
          OrderState.tradeProtection,
        ].contains(d.state);
  }

  /// 等待支付中
  bool get waitingPay => data.value?.state == OrderState.waitingPay;

  /// 是否需要显示暂挂提醒
  bool get needsPendingNotice =>
      data.value?.state == OrderState.tradeOfferPending;

  /// 退款/回款状态1:进行中;2:已完成;3:失败
  int get paymentStatus => data.value?.base.paymentStatus ?? 0;

  /// 需要退款信息。 退款/回款状态1:进行中;2:已完成;3:失败 4以上不显示
  bool get needsRefundCard =>
      data.value?.isBuyer == true &&
      [OrderState.failure, OrderState.exception].contains(data.value?.state) &&
      paymentStatus > 1 &&
      paymentStatus < 4;
  String get refundTime => data.value?.base.stateTime ?? '';

  bool get enableCancel =>
      data.value?.enableCancel == true &&
      (data.value?.state != OrderState.success &&
          data.value?.state != OrderState.failure);

  /// 是否显示问题反馈
  bool get enableFeedback => data.value?.enableFeedback == true;

  /// 计算orderTime是否超过30分钟 可操作取消订单 返回剩余分钟 超时返回0
  int get orderTimeOver30Minutes {
    final orderTime = data.value!.createTime;
    if (orderTime.isEmpty) {
      return 0;
    }
    final orderTimeDate = DateTime.parse(orderTime);
    final now = DateTime.now();
    final diff = now.difference(orderTimeDate);
    final diffMinutes = diff.inMinutes;
    if (diffMinutes > 30) {
      return 0;
    }
    return 30 - diffMinutes;
  }

  String get priceTitle {
    if (data.value?.isBuyer == true) {
      switch (data.value?.state) {
        case null:
        case OrderState.none:
          return "";
        case OrderState.waitingPay:
          return "应付金额";
        case OrderState.waitingCreateTradeOffer:
        case OrderState.waitingResponseTradeOffer:
        case OrderState.waitingConfirmTradeOffer:
        case OrderState.waitingReceiveTradeOffer:
        case OrderState.tradeOfferPending:
        case OrderState.success:
        case OrderState.tradeProtection:
          return "实付金额";
        case OrderState.failure:
        case OrderState.exception:
          return data.value?.base.paymentStatus == 4 ? "应付金额" : "实付金额";
      }
    } else {
      switch (data.value?.state) {
        case null:
        case OrderState.none:
          return "";
        case OrderState.waitingPay:
        case OrderState.waitingCreateTradeOffer:
        case OrderState.waitingResponseTradeOffer:
        case OrderState.waitingConfirmTradeOffer:
        case OrderState.waitingReceiveTradeOffer:
        case OrderState.tradeOfferPending:
        case OrderState.failure:
        case OrderState.exception:
        case OrderState.tradeProtection:
          return "预计收入";
        case OrderState.success:
          return "实收金额";
      }
    }
  }

  BffInterfaceBuyerSteamInfo200ResponseDataResultInner? get guardInfo =>
      data.value?.buyerGuard;

  OrderDetailController({required this.orderId});

  Future<ApiResult<bool>> cancel() async {
    try {
      final res = await Api.to.getBffHttpBffInterfaceApi().cancelOrder(
          v1CancelOrderRequest: V1CancelOrderRequest((b) => b
            ..uid = UserStore.to.uId
            ..orderId = orderId));
      if (res.data?.code == 0) {
        eventBus.fire(OrderCancelledEvent(orderId: orderId));
        return Result.success(res.data?.code == 0);
      }
      return Result.failure(ApiResponse.fromException(Exception('No data')));
    } catch (e) {
      return Result.failure(ApiResponse.fromException(e));
    }
  }

  /// 获取订单数据
  Future<OrderDetailData?> _fetchOrderData() async {
    final res = await Api.to.getBffHttpBffInterfaceApi().orderList(
        v1OrderListRequest: V1OrderListRequest((b) => b
          ..uid = UserStore.to.uId
          ..page = 1
          ..pageSize = 1
          ..orderIds = ListBuilder([orderId])));

    if (res.data?.code != 0 || res.data?.data == null) {
      throw ApiResponse.fromResponse(res);
    }

    final d = res.data!.data;
    final base = res.data!.data!.items!.first;
    var data = OrderDetailData(
      base: base,
      info: findStyledGoodsInfo(d!.goodsInfos, base.styleId, base.goodsId)!,
      stickers: createOrderStickers(
          base.stickers!, base.insignias!, base.keychains, d.goodsInfos!),
    );

    /// 卖家，且交易未完成时显示令牌
    if (data.isBuyer == false &&
        [
          OrderState.waitingResponseTradeOffer,
          OrderState.waitingConfirmTradeOffer,
          OrderState.tradeOfferPending,
        ].contains(data.state)) {
      final guardRes = await getGuard(orderId: orderId);
      final guard = guardRes.getOrNull();
      if (guard != null) {
        data = data.copyWith(buyerGuard: guard);
      }
    }

    return data;
  }

  void onRefresh() async {
    try {
      changePageStateToLoading();
      final data = await _fetchOrderData();
      this.data.value = data;
      changePageStateToDone();
    } catch (e) {
      changePageStateToError(e);
    }
  }

  /// 交易保护状态刷新
  Future<void> onTradeProtectionRefresh() async {
    try {
      tradeProtectionRefreshing.value = true;
      final data = await _fetchOrderData();
      final context = rootWidgetKey.currentContext;
      AppToast.info(context!, "上架成功");
     
      this.data.value = data;
    } finally {
      tradeProtectionRefreshing.value = false;
    }
  }

  /// 刷新令牌
  Future<Result<bool, String>> onRefreshGuard() async {
    final data = this.data.value;
    if (data == null) {
      return const Result.failure("暂无订单数据");
    }
    final res = await refreshGuard(orderId: orderId);
    final guard = res.getOrNull();
    final fail = res.exceptionOrNull();
    if (guard != null) {
      this.data.value = data.copyWith(buyerGuard: guard);
      return const Result.success(true);
    }
    return Result.failure(fail?.message ?? "刷新令牌失败，请重试");
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onTradeOfferCreated(TradeOfferCreatedEvent event) {
    super.onTradeOfferCreated(event);
    onRefresh();
  }

  @override
  void onTradeOfferAccept(TradeOfferAcceptEvent event) {
    super.onTradeOfferAccept(event);
    onRefresh();
  }

  @override
  void onDetached() {
    // TODO: implement onDetached
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }

  @override
  void onInactive() {
    // TODO: implement onInactive
  }

  @override
  void onPaused() {
    // TODO: implement onPaused
  }

  @override
  void onResumed() {
    if (AppRouter.to.currentPath.startsWith('/orders/')) {
      onRefresh();
    }
  }
}
